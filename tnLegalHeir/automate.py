from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time

# List of form numbers
form_ids = ["TN-720231229452", "TN-720231229453"]

# Initialize WebDriver
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
driver.get("https://tnedistrict.tn.gov.in/mislogin/home.xhtml")

results = {}

for form_id in form_ids:
    try:
        # Wait for the search box to be present
        search_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "j_idt10:j_idt13"))
        )

        # Clear and enter the form ID
        search_input.clear()
        search_input.send_keys(form_id)
        search_input.send_keys(Keys.ENTER)

        # Wait for the applicant name element to be present
        applicant_name_span = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "statusform:applicantName"))
        )

        # Extract the name
        applicant_name = applicant_name_span.text
        results[form_id] = applicant_name
        print(f"{form_id}: {applicant_name}")
        
    except Exception as e:
        results[form_id] = f"Error: {str(e)}"
        print(f"{form_id}: Failed - {e}")
        
    # Optionally wait between queries to avoid rate limiting
    time.sleep(1)

driver.quit()

# Final result
print("\nFinal Results:")
for k, v in results.items():
    print(f"{k}: {v}")
